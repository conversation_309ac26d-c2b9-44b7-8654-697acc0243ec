# 删除原文件功能使用说明

## 功能概述

新增的 `--delete-source` 功能可以在翻译完成后自动删除原始JSON文件，防止重复翻译，节省存储空间。

## 使用方法

### 基本语法

```bash
python json_translator_batch.py --input <输入目录> --output <输出目录> --delete-source
```

### 完整参数示例

```bash
# 启用删除原文件功能
python json_translator_batch.py \
    --input json/ \
    --output translated/ \
    --delete-source \
    --debug \
    --model qwen-plus
```

## 功能特性

### ✅ 安全机制

1. **翻译成功后才删除**: 只有在JSON文件成功翻译并保存到输出目录后，才会删除原文件
2. **错误处理**: 如果翻译失败，原文件会被保留
3. **删除失败提醒**: 如果删除原文件失败，会显示警告但不影响翻译流程
4. **详细日志**: 在调试模式下记录每个文件的删除状态

### 📊 统计信息

程序会显示详细的处理统计：

```
翻译完成! 共翻译了 156 个不同的字符串
成功处理: 10/10 个文件
已删除原文件: 10 个
翻译结果保存在: translated/
```

### ⚠️ 安全警告

启用删除功能时会显示警告提示：

```
⚠ 警告: 启用了删除原文件模式，翻译完成后将删除原始文件
```

## 使用场景

### 1. 批量迁移翻译

```bash
# 将 source/ 目录中的文件翻译后移动到 target/
python json_translator_batch.py \
    --input source/ \
    --output target/ \
    --delete-source
```

### 2. 节省存储空间

```bash
# 翻译大量文件时节省磁盘空间
python json_translator_batch.py \
    --input large_dataset/ \
    --output translated_dataset/ \
    --delete-source \
    --pattern "**/*.json"
```

### 3. 防止重复处理

```bash
# 确保每个文件只被翻译一次
python json_translator_batch.py \
    --input pending/ \
    --output completed/ \
    --delete-source
```

## 安全建议

### 🔒 使用前备份

```bash
# 建议在使用删除功能前先备份原文件
cp -r original_files/ backup_files/

# 然后再运行翻译
python json_translator_batch.py \
    --input original_files/ \
    --output translated_files/ \
    --delete-source
```

### 🧪 先测试小批量

```bash
# 先用少量文件测试功能
python json_translator_batch.py \
    --input test_sample/ \
    --output test_output/ \
    --delete-source \
    --debug
```

### 📝 启用调试日志

```bash
# 使用调试模式查看详细处理过程
python json_translator_batch.py \
    --input data/ \
    --output output/ \
    --delete-source \
    --debug
```

## 错误处理

### 翻译失败时

- ✅ 原文件会被保留
- ✅ 错误信息会被记录
- ✅ 程序继续处理其他文件

### 删除失败时

- ✅ 显示警告信息
- ✅ 翻译结果仍然有效
- ✅ 不影响后续文件处理

### 程序中断时

- ✅ 已翻译的文件对应的原文件会被删除
- ✅ 未处理的文件会被保留
- ✅ 显示处理进度统计

## 日志示例

### 正常处理日志

```
[2025-01-03 10:30:15] [INFO] 开始处理文件: input/test.json -> output/test.json
[2025-01-03 10:30:20] [INFO] 翻译完成，输出文件已保存: output/test.json
[2025-01-03 10:30:20] [INFO] 原始文件已删除: input/test.json
✓ 已删除原文件: input/test.json
```

### 删除失败日志

```
[2025-01-03 10:30:20] [ERROR] 删除原文件失败: input/test.json, 错误: Permission denied
⚠ 警告: 删除原文件失败: input/test.json - Permission denied
```

## 注意事项

1. **不可逆操作**: 删除的文件无法恢复，请谨慎使用
2. **权限要求**: 确保对原文件有删除权限
3. **网络中断**: 如果翻译过程中网络中断，已翻译的文件对应的原文件可能已被删除
4. **存储空间**: 确保输出目录有足够空间存储翻译后的文件

## 测试功能

运行测试脚本验证功能：

```bash
python test_delete_source.py
```

测试脚本会创建临时文件，测试删除功能，然后自动清理。
