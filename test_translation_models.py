#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试翻译专用模型功能的脚本
"""

import json
import tempfile
import os
import sys

def create_test_data():
    """创建测试数据"""
    return {
        "title": "Early Pregnancy Symptoms",
        "description": "Common signs that may indicate pregnancy",
        "symptoms": [
            "Nausea",
            "Morning sickness", 
            "Fatigue",
            "Breast tenderness",
            "Missed period"
        ],
        "content_blocks": [
            {
                "type": "paragraph",
                "text": "Pregnancy symptoms can vary significantly from woman to woman."
            },
            {
                "type": "list",
                "list_items": [
                    "Nausea and vomiting",
                    "Increased urination",
                    "Food aversions",
                    "Mood changes"
                ]
            }
        ],
        "url": "https://example.com/pregnancy-symptoms",
        "scraped_at": "2025-01-03"
    }

def test_translation_model(model_name):
    """测试指定的翻译模型"""
    print(f"\n=== 测试模型: {model_name} ===")
    
    # 创建临时目录和文件
    temp_dir = tempfile.mkdtemp(prefix=f"test_{model_name.replace('-', '_')}_")
    input_dir = os.path.join(temp_dir, "input")
    output_dir = os.path.join(temp_dir, "output")
    
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建测试文件
        test_file = os.path.join(input_dir, "test_symptoms.json")
        test_data = create_test_data()
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"测试文件已创建: {test_file}")
        print(f"使用模型: {model_name}")
        
        # 构建命令行参数
        cmd_args = [
            "--input", input_dir,
            "--output", output_dir,
            "--model", model_name,
            "--debug"
        ]
        
        # 模拟命令行参数
        original_argv = sys.argv.copy()
        sys.argv = ["json_translator_batch.py"] + cmd_args
        
        try:
            # 重新导入模块以清除之前的状态
            if 'json_translator_batch' in sys.modules:
                del sys.modules['json_translator_batch']
            
            import json_translator_batch
            
            # 运行翻译
            json_translator_batch.main()
            
            # 检查结果
            output_file = os.path.join(output_dir, "test_symptoms.json")
            if os.path.exists(output_file):
                print(f"✓ 翻译完成，输出文件: {output_file}")
                
                # 读取并显示翻译结果
                with open(output_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                
                print("\n翻译结果示例:")
                print(f"标题: {result.get('title', 'N/A')}")
                print(f"描述: {result.get('description', 'N/A')}")
                
                if 'symptoms' in result:
                    print("症状列表:")
                    for i, symptom in enumerate(result['symptoms'][:3], 1):
                        print(f"  {i}. {symptom}")
                
                if 'content_blocks' in result:
                    for block in result['content_blocks']:
                        if block.get('type') == 'list' and 'list_items' in block:
                            print("列表项目:")
                            for i, item in enumerate(block['list_items'][:3], 1):
                                print(f"  {i}. {item}")
                            break
                
                return True
            else:
                print(f"✗ 翻译失败，未找到输出文件")
                return False
                
        except Exception as e:
            print(f"✗ 翻译过程出错: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # 恢复原始命令行参数
            sys.argv = original_argv
            
    finally:
        # 清理临时文件
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"临时目录已清理: {temp_dir}")
        except:
            print(f"警告: 无法清理临时目录 {temp_dir}")

def test_model_detection():
    """测试模型检测功能"""
    print("\n=== 测试模型检测功能 ===")
    
    # 导入模块
    import json_translator_batch
    
    test_cases = [
        ("qwen-mt-turbo", True),
        ("qwen-mt-plus", True),
        ("QWEN-MT-TURBO", True),  # 测试大小写
        ("qwen-plus", False),
        ("qwen-turbo", False),
        ("gpt-4", False)
    ]
    
    for model, expected in test_cases:
        result = json_translator_batch.is_translation_model(model)
        status = "✓" if result == expected else "✗"
        print(f"{status} {model}: {'翻译模型' if result else '通用模型'}")

def main():
    """主测试函数"""
    print("翻译专用模型功能测试")
    print("=" * 50)
    
    try:
        # 测试模型检测功能
        test_model_detection()
        
        # 测试翻译专用模型
        models_to_test = [
            "qwen-mt-plus",    # 高质量翻译模型
            "qwen-mt-turbo",   # 快速翻译模型
            "qwen-plus"        # 对比：通用模型
        ]
        
        results = {}
        for model in models_to_test:
            try:
                results[model] = test_translation_model(model)
            except Exception as e:
                print(f"测试模型 {model} 时出错: {e}")
                results[model] = False
        
        # 输出测试总结
        print("\n" + "=" * 50)
        print("测试总结:")
        for model, success in results.items():
            status = "✓ 成功" if success else "✗ 失败"
            model_type = "翻译专用" if model.startswith("qwen-mt") else "通用模型"
            print(f"{status} {model} ({model_type})")
        
        success_count = sum(results.values())
        total_count = len(results)
        print(f"\n总体结果: {success_count}/{total_count} 个模型测试成功")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
